import { useState } from "react";
import "./App.css";

function App() {
  const handleclick = async () => {};
  // const handleslider = async () => {};
  const [password, setpassword] = useState(null);
  const [slider, setslider] = useState(6);
  const [number, setnumber] = useState(false);
  const [char, setchar] = useState(false);
  return (
    <div>
      <h1>Password generator</h1>
      <div className="showpassword">
        <div className="password"></div>
        <div className="copy">Copy</div>
      </div>
      <div className="div2">
        <div>
          <input
            value={slider}
            // onClick={handleslider}
            type="range"
            id="slider"
            min="6"
            max="20"
          />
        </div>
        <div className="box">
          <input onClick={setnumber(!number)} type="radio" />
          <p>Number</p>
        </div>
        <div className="box">
          <input onClick={setchar(!char)} type="radio" />
          <p>Special Char</p>
        </div>
      </div>
      <div className="button">
        <button onClick={handleclick}>Generate Password</button>
      </div>
    </div>
  );
}

export default App;
